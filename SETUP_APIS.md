# 🔧 Configuração das APIs para Busca de Produtos

Este sistema usa APIs reais para buscar produtos e remover fundos automaticamente.

## 📋 APIs Necessárias

### 1. Unsplash API (Gratuita)
**Para buscar imagens de produtos**

1. Acesse: https://unsplash.com/developers
2. Crie uma conta ou faça login
3. Clique em "New Application"
4. Preencha os dados:
   - Application name: "Encarte Pro"
   - Description: "Sistema de criação de encartes"
   - Aceite os termos de uso
5. Copie o **Access Key**

### 2. Remove.bg API (50 imagens grátis/mês)
**Para remover fundos automaticamente**

1. Acesse: https://www.remove.bg/api
2. Crie uma conta ou faça login
3. Clique em "Get API Key"
4. Copie a **API Key**

## ⚙️ Configuração

### Passo 1: Criar arquivo .env
```bash
# Copie o arquivo de exemplo
cp .env.example .env
```

### Passo 2: Adicionar suas chaves
Edite o arquivo `.env` e substitua pelos valores reais:

```env
VITE_UNSPLASH_ACCESS_KEY=sua_chave_unsplash_aqui
VITE_REMOVEBG_API_KEY=sua_chave_removebg_aqui
```

### Passo 3: Reiniciar o servidor
```bash
npm run dev
```

## 🚀 Como Usar

1. **Digite o nome do produto** no campo de busca
2. **Clique em "Buscar Produto Online"**
3. **Aguarde o processamento** (busca + remoção de fundo)
4. **Produtos aparecerão** na seção "Produtos Encontrados Online"
5. **Clique no produto** para adicionar ao encarte

## ✅ Funcionalidades

- ✅ Busca real de produtos no Unsplash
- ✅ Remoção automática de fundo
- ✅ Integração direta com o canvas
- ✅ Cache de imagens processadas
- ✅ Tratamento de erros
- ✅ Validação de APIs

## 🔍 Exemplo de Busca

- **"coca cola"** → Encontra imagens de Coca-Cola
- **"smartphone"** → Encontra imagens de celulares
- **"pizza"** → Encontra imagens de pizza
- **"notebook"** → Encontra imagens de laptops

## 🛠️ Troubleshooting

### Erro: "APIs não configuradas"
- Verifique se o arquivo `.env` existe
- Confirme se as chaves estão corretas
- Reinicie o servidor

### Erro: "Nenhum produto encontrado"
- Tente termos de busca em inglês
- Use palavras mais genéricas
- Verifique sua conexão com internet

### Erro: "Falha ao remover fundo"
- Verifique se ainda tem créditos na Remove.bg
- A imagem original será usada como fallback

## 💡 Dicas

- **Termos em inglês** funcionam melhor
- **Palavras simples** geram melhores resultados
- **50 remoções de fundo grátis** por mês no Remove.bg
- **5000 buscas grátis** por hora no Unsplash

## 🔒 Segurança

- As chaves ficam apenas no seu `.env` local
- Nunca commite o arquivo `.env` no Git
- Use variáveis de ambiente em produção
