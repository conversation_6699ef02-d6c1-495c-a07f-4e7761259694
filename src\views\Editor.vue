<template>
  <div class="h-screen flex flex-col bg-gray-100">
    <!-- Header do Editor -->
    <header class="bg-white border-b h-12">
      <div class="flex items-center justify-between px-4 h-full">
        <div class="flex items-center space-x-3">
          <button @click="$router.push('/')" class="flex items-center text-blue-600 hover:text-blue-700 text-sm">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Voltar
          </button>
          <div class="flex items-center space-x-2">
            <div class="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
              <span class="text-white font-bold text-xs">E</span>
            </div>
            <h1 class="text-base font-medium text-gray-800">encartefacil</h1>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
            Salvar
          </button>
          <button class="px-3 py-1 text-sm bg-emerald-500 text-white rounded hover:bg-emerald-600">
            Exportar
          </button>
          <button class="p-1 text-gray-600 hover:text-gray-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
          </button>
          <button class="p-1 text-gray-600 hover:text-gray-800">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <div class="flex flex-1 overflow-hidden">
      <!-- Sidebar Esquerda - Menu Principal -->
      <div style="width: 64px; background-color: #ffffff; border-right: 1px solid #d1d5db; display: flex; flex-direction: column; align-items: center; padding: 12px 0; gap: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
        <button
          v-for="tool in sidebarTools"
          :key="tool.id"
          @click="activeTool = tool.id"
          :style="{
            width: '48px',
            height: '48px',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transition: 'all 0.2s',
            border: '1px solid',
            backgroundColor: activeTool === tool.id ? '#10b981' : '#ffffff',
            borderColor: activeTool === tool.id ? '#10b981' : '#d1d5db',
            color: activeTool === tool.id ? '#ffffff' : '#374151',
            boxShadow: activeTool === tool.id ? '0 4px 6px rgba(0,0,0,0.1)' : 'none'
          }"
          :title="tool.name"
        >
          <span style="font-size: 20px; font-weight: bold;">{{ tool.icon }}</span>
        </button>
      </div>

      <!-- Panel Lateral - Temas ou Produtos -->
      <div class="w-64 bg-white border-r border-gray-200 overflow-y-auto shadow-sm">
        <!-- Menu de Temas -->
        <div v-if="activeTool === 'themes'">
          <!-- Header -->
          <div class="p-3 border-b">
            <div class="flex items-center justify-between">
              <h3 class="text-sm font-semibold text-gray-800">Temas</h3>
              <div class="flex items-center space-x-1 text-xs text-gray-500">
                <button class="p-1 hover:bg-gray-200 rounded">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                  </svg>
                </button>
                <button class="p-1 hover:bg-gray-200 rounded">
                  <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                  </svg>
                </button>
                <span>SPL</span>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1">Escolha o melhor tema para o seu encarte</p>
          </div>

          <!-- Grid de Temas -->
          <div class="p-3">
            <div class="grid grid-cols-2 gap-2">
              <!-- Tema São João -->
              <div 
                class="group cursor-pointer"
                @click="selectTheme(saoJoaoTheme)"
              >
                <div class="aspect-[3/4] rounded-lg overflow-hidden border-2 border-yellow-400">
                  <img 
                    src="/encarte.png" 
                    alt="São João"
                    class="w-full h-full object-cover"
                    @error="handleImageError"
                  >
                </div>
                <div class="text-xs text-gray-700 mt-1 text-center font-medium">São João</div>
              </div>
              
              <!-- Tema Abril Mais -->
              <div 
                class="group cursor-pointer"
                @click="selectTheme(abrilMaisTheme)"
              >
                <div class="aspect-[3/4] rounded-lg overflow-hidden bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
                  <div class="text-center text-white">
                    <div class="text-xs font-bold">Abril Mais</div>
                  </div>
                </div>
                <div class="text-xs text-gray-700 mt-1 text-center">Abril Mais</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Menu de Produtos e Configurações -->
        <div v-else-if="activeTool === 'products'">
          <!-- Header -->
          <div class="p-3 border-b">
            <h3 class="text-sm font-semibold text-gray-800">Produtos e configurações</h3>
            <p class="text-xs text-gray-500 mt-1">Adicione produtos, textos e formas ao seu encarte</p>
          </div>

          <!-- Tabs -->
          <div class="border-b">
            <div class="flex">
              <button
                @click="activeTab = 'produtos'"
                :class="[
                  'flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors',
                  activeTab === 'produtos' ? 'border-emerald-500 text-emerald-600 bg-emerald-50' : 'border-transparent text-gray-600 hover:text-gray-800'
                ]"
              >
                Produtos
              </button>
              <button
                @click="activeTab = 'informacoes'"
                :class="[
                  'flex-1 px-3 py-2 text-sm font-medium border-b-2 transition-colors',
                  activeTab === 'informacoes' ? 'border-emerald-500 text-emerald-600 bg-emerald-50' : 'border-transparent text-gray-600 hover:text-gray-800'
                ]"
              >
                Informações
              </button>
            </div>
          </div>

          <!-- Conteúdo das Tabs -->
          <div class="p-3">
            <!-- Tab Produtos -->
            <div v-if="activeTab === 'produtos'">
              <p class="text-xs text-gray-600 mb-3">Digite o nome do produto para buscar online com fundo removido automaticamente</p>
              
              <!-- Campo de busca -->
              <div class="mb-3">
                <input
                  v-model="productSearch"
                  @input="searchProducts"
                  type="text"
                  placeholder="Digite o nome do produto..."
                  class="w-full px-3 py-2 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
                >
              </div>

              <!-- Botão Buscar -->
              <button
                @click="searchProductOnline"
                :disabled="isSearching || !productSearch.trim()"
                class="w-full bg-emerald-500 text-white py-2 rounded text-sm font-medium hover:bg-emerald-600 transition-colors mb-4 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {{ isSearching ? 'Buscando...' : 'Buscar Produto Online' }}
              </button>

              <!-- Lista de Produtos do Encarte -->
              <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-800 mb-2">Produtos do Encarte</h4>
                <div class="space-y-2 max-h-64 overflow-y-auto">
                  <div 
                    v-for="product in canvasProducts" 
                    :key="product.id"
                    class="flex items-center p-2 bg-gray-50 rounded border"
                  >
                    <div class="w-8 h-8 bg-gray-200 rounded mr-2 flex items-center justify-center">
                      <img 
                        v-if="product.image" 
                        :src="product.image" 
                        :alt="product.name"
                        class="w-full h-full object-cover rounded"
                      >
                      <span v-else class="text-xs text-gray-400">IMG</span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="text-xs font-medium text-gray-800 truncate">{{ product.name }}</div>
                      <div class="text-xs text-emerald-600 font-semibold">{{ product.price }}</div>
                    </div>
                    <button class="text-gray-400 hover:text-red-500 ml-2">
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Resultados da Busca Online -->
              <div v-if="searchResults.length > 0" class="mb-4">
                <h4 class="text-sm font-medium text-gray-800 mb-2 flex items-center">
                  <span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                  Produtos Encontrados Online
                </h4>
                <div class="space-y-1 max-h-32 overflow-y-auto">
                  <div
                    v-for="product in searchResults"
                    :key="product.id"
                    class="flex items-center p-2 border border-green-200 bg-green-50 rounded cursor-pointer hover:bg-green-100 transition-colors"
                    @click="addProductToCanvas(product)"
                  >
                    <div class="w-8 h-8 bg-white rounded mr-2 flex items-center justify-center overflow-hidden border">
                      <img
                        v-if="product.processedImage || product.image"
                        :src="product.processedImage || product.image"
                        :alt="product.name"
                        class="w-full h-full object-cover"
                      >
                      <span v-else class="text-xs text-gray-400">IMG</span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="text-xs font-medium text-gray-800 truncate">{{ product.name }}</div>
                      <div class="text-xs text-green-600 font-medium">{{ product.category }}</div>
                      <div class="text-xs text-green-700 font-semibold">{{ product.price }}</div>
                    </div>
                    <div class="text-xs bg-green-200 text-green-800 px-1 rounded">SEM FUNDO</div>
                  </div>
                </div>
              </div>

              <!-- Lista de Produtos Disponíveis -->
              <div>
                <h4 class="text-sm font-medium text-gray-800 mb-2">Produtos Locais</h4>
                <div class="space-y-1 max-h-48 overflow-y-auto">
                  <div
                    v-for="product in filteredProducts.filter(p => !searchResults.find(sr => sr.id === p.id))"
                    :key="product.id"
                    class="flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50 transition-colors"
                    @click="addProductToCanvas(product)"
                  >
                    <div class="w-8 h-8 bg-gray-100 rounded mr-2 flex items-center justify-center overflow-hidden">
                      <img
                        v-if="product.image"
                        :src="product.image"
                        :alt="product.name"
                        class="w-full h-full object-cover"
                      >
                      <span v-else class="text-xs text-gray-400">IMG</span>
                    </div>
                    <div class="flex-1 min-w-0">
                      <div class="text-xs font-medium text-gray-800 truncate">{{ product.name }}</div>
                      <div class="text-xs text-gray-500">{{ product.category }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Tab Informações -->
            <div v-if="activeTab === 'informacoes'">
              <div class="space-y-3">
                <div>
                  <label class="block text-xs font-medium text-gray-700 mb-1">Nome do Estabelecimento</label>
                  <input
                    type="text"
                    placeholder="Digite o nome..."
                    class="w-full px-2 py-1.5 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-emerald-500"
                  >
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-700 mb-1">Endereço</label>
                  <input
                    type="text"
                    placeholder="Digite o endereço..."
                    class="w-full px-2 py-1.5 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-emerald-500"
                  >
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-700 mb-1">Telefone</label>
                  <input
                    type="text"
                    placeholder="Digite o telefone..."
                    class="w-full px-2 py-1.5 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-emerald-500"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Área Principal do Canvas -->
      <div class="flex-1 flex flex-col bg-gray-200">
        <!-- Toolbar Superior -->
        <div class="bg-white border-b px-3 py-1.5 flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <button 
              @click="undo"
              class="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              :disabled="!canUndo"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
              </svg>
            </button>
            <button 
              @click="redo"
              class="p-1 text-gray-600 hover:text-gray-800 disabled:opacity-50"
              :disabled="!canRedo"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6"></path>
              </svg>
            </button>
            <div class="border-l h-4 mx-1"></div>
            <button 
              @click="addProductName"
              class="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
              title="Adicionar nome do produto"
            >
              Nome
            </button>
            <button 
              @click="addProductPrice"
              class="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
              title="Adicionar preço"
            >
              Preço
            </button>
            <div class="border-l h-4 mx-1"></div>
            <button 
              @click="deleteSelected"
              class="p-1 text-gray-600 hover:text-red-600"
              :disabled="!selectedObject"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <button 
              @click="zoomOut"
              class="p-1 text-gray-600 hover:text-gray-800"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7"></path>
              </svg>
            </button>
            <span class="text-xs text-gray-600 min-w-8 text-center">{{ Math.round(zoom * 100) }}%</span>
            <button 
              @click="zoomIn"
              class="p-1 text-gray-600 hover:text-gray-800"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Canvas Container -->
        <div class="flex-1 p-4 flex items-center justify-center">
          <div class="canvas-container bg-white shadow-sm">
            <canvas ref="canvasRef" id="fabricCanvas"></canvas>
          </div>
        </div>
      </div>

      <!-- Sidebar Direita - Detalhes do Produto -->
      <div class="w-80 bg-white border-l overflow-y-auto">
        <!-- Header -->
        <div class="p-4 border-b bg-gray-50">
          <div class="flex items-center space-x-3">
            <button class="text-gray-600 hover:text-gray-800">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>
            <div>
              <h3 class="text-lg font-semibold text-gray-800">Detalhes do produto</h3>
              <p class="text-sm text-gray-500">Detalhes do produto</p>
            </div>
          </div>
        </div>

        <div v-if="selectedProduct" class="p-4 space-y-6">
          <!-- Imagem do Produto -->
          <div class="bg-gray-100 rounded-lg p-6 text-center">
            <div class="w-24 h-32 mx-auto mb-4 bg-white rounded-lg shadow-sm flex items-center justify-center overflow-hidden">
              <img 
                v-if="selectedProduct.image" 
                :src="selectedProduct.image" 
                :alt="selectedProduct.name"
                class="w-full h-full object-contain"
              >
              <span v-else class="text-gray-400 text-xs">IMG</span>
            </div>
            <button class="text-sm text-gray-600 hover:text-gray-800">Ver mais imagens</button>
          </div>

          <!-- Opções de Edição -->
          <div class="space-y-3">
            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Remover fundo</span>
              </div>
              <div class="w-6 h-6 bg-yellow-100 rounded flex items-center justify-center">
                <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              </div>
            </button>

            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Cor de fundo</span>
              </div>
            </button>

            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700">Cor da etiqueta</span>
              </div>
            </button>

            <button class="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 text-red-600">
              <div class="flex items-center space-x-3">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span class="text-sm font-medium">Remover produto</span>
              </div>
            </button>
          </div>

          <!-- Formulário de Detalhes -->
          <div class="space-y-4">
            <!-- Nome -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Nome</label>
              <input
                v-model="selectedProduct.name"
                @input="updateSelectedProduct"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                placeholder="Nome do produto"
              >
            </div>

            <!-- Medida -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Medida</label>
              <div class="flex space-x-2">
                <input
                  v-model="selectedProduct.measure"
                  @input="updateSelectedProduct"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                  placeholder="0"
                >
                <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent">
                  <option>kg</option>
                  <option>g</option>
                  <option>L</option>
                  <option>ml</option>
                  <option>un</option>
                </select>
              </div>
            </div>

            <!-- Tipo -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tipo</label>
              <select
                v-model="selectedProduct.type"
                @change="updateSelectedProduct"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
              >
                <option>Simples</option>
                <option>Composto</option>
                <option>Variável</option>
              </select>
            </div>

            <!-- Preço único -->
            <div class="text-sm text-gray-500 mb-2">Preço único</div>

            <!-- Preço oferta -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Preço oferta</label>
              <input
                v-model="selectedProduct.price"
                @input="updateSelectedProduct"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                placeholder="0,00"
              >
            </div>

            <!-- Tamanhos -->
            <div>
              <div class="flex items-center space-x-2 mb-3">
                <label class="text-sm font-medium text-gray-700">Tamanhos</label>
                <div class="w-6 h-6 bg-yellow-100 rounded flex items-center justify-center">
                  <svg class="w-4 h-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                </div>
              </div>

              <!-- Slider Produto -->
              <div class="mb-4">
                <label class="block text-sm text-gray-600 mb-2">Produto</label>
                <div class="relative">
                  <input 
                    v-model="selectedProduct.productSize"
                    @input="updateSelectedProduct"
                    type="range" 
                    min="0" 
                    max="100" 
                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-green"
                  >
                </div>
              </div>

              <!-- Slider Etiqueta -->
              <div>
                <label class="block text-sm text-gray-600 mb-2">Etiqueta</label>
                <div class="relative">
                  <input 
                    v-model="selectedProduct.labelSize"
                    @input="updateSelectedProduct"
                    type="range" 
                    min="0" 
                    max="100" 
                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-green"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="p-8 text-center text-gray-500">
          <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
          </svg>
          <p class="text-sm">Selecione um produto para editar suas propriedades</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed } from 'vue'
import { fabric } from 'fabric'
import { ProductImageService } from '@/services/productImageService'
import { BackgroundRemovalService } from '@/services/backgroundRemovalService'

const canvasRef = ref<HTMLCanvasElement>()
let canvas: fabric.Canvas | null = null
const selectedObject = ref<any>(null)
const activeTool = ref('themes')
const activeTab = ref('produtos')
const productSearch = ref('')
const zoom = ref(1)
const canUndo = ref(false)
const canRedo = ref(false)
const canvasProducts = ref<any[]>([])
const selectedProduct = ref<any>(null)
const isSearching = ref(false)
const searchResults = ref<any[]>([])

const sidebarTools = ref([
  { id: 'themes', name: 'Temas', icon: '🎨' },
  { id: 'products', name: 'Produtos', icon: '📦' },
  { id: 'shapes', name: 'Formas', icon: '🔷' },
  { id: 'text', name: 'Texto', icon: 'T' }
])

const saoJoaoTheme = ref({ id: 1, name: 'São João', hasTemplate: true })
const abrilMaisTheme = ref({ id: 2, name: 'Abril Mais', hasTemplate: false })

// Definir as áreas dos produtos no template São João (coordenadas ajustadas para não passar do limite)
const productAreas = ref([
  { x: 10, y: 280, width: 330, height: 250 },   // Área 1 - superior esquerdo
  { x: 360, y: 280, width: 330, height: 250 },  // Área 2 - superior direito
  { x: 10, y: 540, width: 330, height: 250 },   // Área 3 - meio esquerdo
  { x: 360, y: 540, width: 330, height: 250 },  // Área 4 - meio direito
  { x: 10, y: 800, width: 330, height: 180 },   // Área 5 - inferior esquerdo
  { x: 360, y: 800, width: 330, height: 180 }   // Área 6 - inferior direito
])

const currentProductIndex = ref(0)

const products = ref([
  { 
    id: 1, 
    name: 'Coca-Cola Lata 350ml', 
    category: 'Bebidas', 
    image: 'https://carrefourbrfood.vtexassets.com/arquivos/ids/119765719/coca-cola-lata-350-ml-1.jpg?v=638224488171270000',
    removeBg: true
  },
  { 
    id: 2, 
    name: 'Fanta Uva Lata 350ml', 
    category: 'Bebidas', 
    image: 'https://images.tcdn.com.br/img/img_prod/1115696/fanta_uva_lata_350ml_6_und_49_1_8ddc4c976e574a4678513f12366effb6.jpg',
    removeBg: true
  },
  { 
    id: 3, 
    name: 'Fanta Laranja Lata 350ml', 
    category: 'Bebidas', 
    image: 'https://images.tcdn.com.br/img/img_prod/1115696/fanta_laranja_lata_350ml_6_und_39_1_ea7725d8f660b15a6ec56f3bf0af2b2b.jpg',
    removeBg: true
  },
  { 
    id: 4, 
    name: 'Cigarro Carlton', 
    category: 'Cigarros', 
    image: 'https://www.lgatacado.com.br/wp-content/uploads/sites/3994/2017/03/Design-sem-nome-9.png',
    removeBg: true
  },
  { id: 5, name: 'Arroz Tio João 5kg', category: 'Grãos', image: null },
  { id: 6, name: 'Leite Integral 1L', category: 'Laticínios', image: null },
  { id: 7, name: 'Pão de Açúcar', category: 'Padaria', image: null },
  { id: 8, name: 'Frango Inteiro', category: 'Carnes', image: null },
  { id: 9, name: 'Banana Prata', category: 'Frutas', image: null },
  { id: 10, name: 'Detergente Ypê', category: 'Limpeza', image: null }
])

// Carregar imagens automaticamente
onMounted(async () => {
  await nextTick()
  initCanvas()
  loadProductImages()
})

const loadProductImages = async () => {
  // Primeiro carregar imagens automáticas para produtos sem imagem
  const productsWithImages = await ProductImageService.getProductsWithImages(products.value)
  
  // Depois processar remoção de fundo para produtos que têm imagem e removeBg = true
  const processedProducts = await BackgroundRemovalService.processProductImages(productsWithImages)
  
  products.value = processedProducts
}

const filteredProducts = computed(() => {
  if (!productSearch.value) return products.value
  return products.value.filter(product => 
    product.name.toLowerCase().includes(productSearch.value.toLowerCase()) ||
    product.category.toLowerCase().includes(productSearch.value.toLowerCase())
  )
})

const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
  target.parentElement!.innerHTML = `
    <div class="w-full h-full bg-gradient-to-br from-yellow-400 to-orange-600 flex items-center justify-center">
      <div class="text-center text-white">
        <div class="text-xs font-bold">São João</div>
      </div>
    </div>
  `
}

const initCanvas = () => {
  if (!canvasRef.value) return
  
  canvas = new fabric.Canvas(canvasRef.value, {
    width: 700,
    height: 1000,
    backgroundColor: '#ffffff'
  })

  canvas.on('selection:created', (e) => {
    const obj = e.selected?.[0]
    updateSelectedObject(obj)
    
    // Se for um grupo de produto, selecionar o produto correspondente
    if (obj && obj.type === 'group') {
      const productId = obj.productId
      if (productId) {
        const product = canvasProducts.value.find(p => p.id === productId)
        if (product) {
          selectedProduct.value = product
        }
      }
    }
  })

  canvas.on('selection:updated', (e) => {
    const obj = e.selected?.[0]
    updateSelectedObject(obj)
    
    // Se for um grupo de produto, selecionar o produto correspondente
    if (obj && obj.type === 'group') {
      const productId = obj.productId
      if (productId) {
        const product = canvasProducts.value.find(p => p.id === productId)
        if (product) {
          selectedProduct.value = product
        }
      }
    }
  })

  canvas.on('selection:cleared', () => {
    selectedObject.value = null
    selectedProduct.value = null
  })

  addInitialTemplate()
}

const searchProducts = () => {
  // A busca é reativa através do computed filteredProducts
}

const searchProductOnline = async () => {
  if (!productSearch.value.trim() || isSearching.value) return

  isSearching.value = true
  searchResults.value = []

  try {
    // Simular busca de produtos online (você pode integrar com APIs reais)
    const mockResults = await searchProductsAPI(productSearch.value)

    // Processar cada resultado para remover fundo
    for (const product of mockResults) {
      if (product.image) {
        try {
          // Remover fundo da imagem
          const imageWithoutBg = await removeImageBackground(product.image)
          product.processedImage = imageWithoutBg
          product.removeBg = true
        } catch (error) {
          console.warn('Erro ao remover fundo:', error)
          product.processedImage = product.image
        }
      }
      searchResults.value.push(product)
    }

    // Adicionar resultados aos produtos disponíveis
    products.value = [...products.value, ...searchResults.value]

  } catch (error) {
    console.error('Erro na busca:', error)
    alert('Erro ao buscar produtos. Tente novamente.')
  } finally {
    isSearching.value = false
  }
}

// Função para simular busca de produtos em APIs externas
const searchProductsAPI = async (query: string) => {
  // Simular delay de API
  await new Promise(resolve => setTimeout(resolve, 2000))

  // Resultados mockados baseados na busca
  const mockProducts = [
    {
      id: Date.now() + 1,
      name: `${query} - Produto 1`,
      category: 'Encontrado Online',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop',
      price: 'R$ 15,90'
    },
    {
      id: Date.now() + 2,
      name: `${query} - Produto 2`,
      category: 'Encontrado Online',
      image: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=300&h=300&fit=crop',
      price: 'R$ 12,50'
    },
    {
      id: Date.now() + 3,
      name: `${query} - Produto 3`,
      category: 'Encontrado Online',
      image: 'https://images.unsplash.com/photo-1567306301408-9b74779a11af?w=300&h=300&fit=crop',
      price: 'R$ 8,75'
    }
  ]

  return mockProducts
}

// Função para remover fundo da imagem
const removeImageBackground = async (imageUrl: string): Promise<string> => {
  try {
    // Simular processamento de remoção de fundo
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Em uma implementação real, você usaria APIs como:
    // - Remove.bg API
    // - Canva API
    // - Ou processamento local com bibliotecas como rembg

    // Por enquanto, retornamos a imagem original
    // mas você pode integrar com serviços reais aqui
    return imageUrl

  } catch (error) {
    console.error('Erro ao remover fundo:', error)
    throw error
  }
}

const addProductToCanvas = (product: any) => {
  if (!canvas) return
  
  const availableArea = getNextAvailableArea()
  
  if (!availableArea) {
    alert('Todas as áreas de produtos estão ocupadas!')
    return
  }
  
  const productWithPrice = {
    ...product,
    price: '101,50',
    originalPrice: 'R$ 101,50',
    measure: '350',
    type: 'Simples',
    productSize: 50,
    labelSize: 50
  }
  canvasProducts.value.push(productWithPrice)
  selectedProduct.value = productWithPrice
  
  const { x, y, width, height } = availableArea

  // Usar imagem processada (sem fundo) se disponível, senão usar imagem original
  const imageUrl = product.processedImage || product.image

  if (imageUrl) {
    fabric.Image.fromURL(imageUrl, (img) => {
      if (!canvas) return
      
      // Calcular escala para a imagem (ocupar 60% da área)
      const maxWidth = width * 0.6
      const maxHeight = height * 0.5
      const scaleX = maxWidth / (img.width || maxWidth)
      const scaleY = maxHeight / (img.height || maxHeight)
      const scale = Math.min(scaleX, scaleY)
      
      img.set({
        left: x + width/2,
        top: y + height/3,
        originX: 'center',
        originY: 'center',
        scaleX: scale,
        scaleY: scale,
        selectable: false
      })
      
      // Criar faixa amarela no topo
      const yellowStrip = new fabric.Rect({
        left: x + 20,
        top: y + height - 100,
        width: width - 40,
        height: 20,
        fill: '#fbbf24', // Cor amarela
        rx: 8,
        ry: 0,
        selectable: false
      })
      
      // Texto na faixa amarela
      const stripText = new fabric.Text('ESTE PRODUTO É CÂNCER', {
        left: x + width/2,
        top: y + height - 95,
        fontSize: 10,
        fill: '#000000',
        fontFamily: 'Inter',
        fontWeight: 'bold',
        textAlign: 'center',
        originX: 'center',
        selectable: false
      })
      
      // Criar etiqueta de preço (fundo azul-verde)
      const priceTag = new fabric.Rect({
        left: x + 20,
        top: y + height - 80,
        width: width - 40,
        height: 60,
        fill: '#0891b2', // Cor azul-verde como na imagem
        rx: 0,
        ry: 8,
        selectable: false
      })
      
      // Texto "R$" menor
      const currencyText = new fabric.Text('R$', {
        left: x + 40,
        top: y + height - 65,
        fontSize: 24,
        fill: '#ffffff',
        fontFamily: 'Inter',
        fontWeight: 'bold',
        selectable: false
      })
      
      // Valor principal grande
      const priceValue = new fabric.Text(productWithPrice.price.split(',')[0] || '0', {
        left: x + 80,
        top: y + height - 75,
        fontSize: 48,
        fill: '#ffffff',
        fontFamily: 'Inter',
        fontWeight: 'bold',
        selectable: false
      })
      
      // Centavos menores
      const centsValue = new fabric.Text(',' + (productWithPrice.price.split(',')[1] || '00'), {
        left: x + width - 80,
        top: y + height - 55,
        fontSize: 24,
        fill: '#ffffff',
        fontFamily: 'Inter',
        fontWeight: 'bold',
        selectable: false
      })
      
      // Criar grupo com todos os elementos
      const productGroup = new fabric.Group([img, yellowStrip, stripText, priceTag, currencyText, priceValue, centsValue], {
        left: x,
        top: y,
        selectable: true
      })
      
      // Adicionar ID do produto ao grupo para identificação
      productGroup.productId = product.id
      
      canvas.add(productGroup)
      canvas.setActiveObject(productGroup)
      canvas.renderAll()
    }, {
      crossOrigin: 'anonymous'
    })
  } else {
    // Para produtos sem imagem, criar placeholder com etiqueta de preço
    const productRect = new fabric.Rect({
      left: x + 10,
      top: y + 10,
      width: width - 20,
      height: height - 120,
      fill: 'rgba(200, 200, 200, 0.3)',
      stroke: 'rgba(150, 150, 150, 0.8)',
      strokeWidth: 2,
      strokeDashArray: [5, 5],
      rx: 8,
      ry: 8,
      selectable: false
    })
    
    const productText = new fabric.Text(product.name, {
      left: x + width/2,
      top: y + height/3,
      fontSize: 14,
      fill: '#666666',
      fontFamily: 'Inter',
      fontWeight: 'bold',
      textAlign: 'center',
      originX: 'center',
      originY: 'center',
      selectable: false
    })
    
    // Faixa amarela
    const yellowStrip = new fabric.Rect({
      left: x + 20,
      top: y + height - 100,
      width: width - 40,
      height: 20,
      fill: '#fbbf24',
      rx: 8,
      ry: 0,
      selectable: false
    })
    
    const stripText = new fabric.Text('ESTE PRODUTO É CÂNCER', {
      left: x + width/2,
      top: y + height - 95,
      fontSize: 10,
      fill: '#000000',
      fontFamily: 'Inter',
      fontWeight: 'bold',
      textAlign: 'center',
      originX: 'center',
      selectable: false
    })
    
    // Etiqueta de preço
    const priceTag = new fabric.Rect({
      left: x + 20,
      top: y + height - 80,
      width: width - 40,
      height: 60,
      fill: '#0891b2',
      rx: 0,
      ry: 8,
      selectable: false
    })
    
    const currencyText = new fabric.Text('R$', {
      left: x + 40,
      top: y + height - 65,
      fontSize: 24,
      fill: '#ffffff',
      fontFamily: 'Inter',
      fontWeight: 'bold',
      selectable: false
    })
    
    const priceValue = new fabric.Text(productWithPrice.price.split(',')[0] || '0', {
      left: x + 80,
      top: y + height - 75,
      fontSize: 48,
      fill: '#ffffff',
      fontFamily: 'Inter',
      fontWeight: 'bold',
      selectable: false
    })
    
    const centsValue = new fabric.Text(',' + (productWithPrice.price.split(',')[1] || '00'), {
      left: x + width - 80,
      top: y + height - 55,
      fontSize: 24,
      fill: '#ffffff',
      fontFamily: 'Inter',
      fontWeight: 'bold',
      selectable: false
    })
    
    const placeholderGroup = new fabric.Group([productRect, productText, yellowStrip, stripText, priceTag, currencyText, priceValue, centsValue], {
      left: x,
      top: y,
      selectable: true
    })
    
    // Adicionar ID do produto ao grupo para identificação
    placeholderGroup.productId = product.id
    
    canvas.add(placeholderGroup)
    canvas.setActiveObject(placeholderGroup)
    canvas.renderAll()
  }
  
  currentProductIndex.value++
}

const getNextAvailableArea = () => {
  if (currentProductIndex.value >= productAreas.value.length) {
    return null
  }
  return productAreas.value[currentProductIndex.value]
}

const resetProductAreas = () => {
  currentProductIndex.value = 0
}

const updateSelectedObject = (obj: any) => {
  if (obj) {
    selectedObject.value = {
      type: obj.type,
      left: Math.round(obj.left || 0),
      top: Math.round(obj.top || 0),
      text: obj.text || '',
      fontSize: obj.fontSize || 20
    }
  }
}

const updateObject = () => {
  if (!canvas || !selectedObject.value) return
  
  const activeObject = canvas.getActiveObject()
  if (activeObject) {
    activeObject.set({
      left: selectedObject.value.left,
      top: selectedObject.value.top,
      text: selectedObject.value.text,
      fontSize: selectedObject.value.fontSize
    })
    canvas.renderAll()
  }
}

const deleteSelected = () => {
  if (!canvas) return
  
  const activeObject = canvas.getActiveObject()
  if (activeObject) {
    canvas.remove(activeObject)
  }
}

const selectTheme = (theme: any) => {
  if (!canvas) return
  
  if (theme.name === 'São João' && theme.hasTemplate) {
    loadSaoJoaoTemplate()
  } else {
    canvas.clear()
    canvas.backgroundColor = '#ffffff'
    canvas.renderAll()
  }
}

const loadSaoJoaoTemplate = () => {
  if (!canvas) return
  
  canvas.clear()
  resetProductAreas()
  canvasProducts.value = []
  
  fabric.Image.fromURL('/encarte.png', (img) => {
    if (!canvas) return
    
    img.set({
      left: 0,
      top: 0,
      scaleX: canvas.width! / (img.width || canvas.width!),
      scaleY: canvas.height! / (img.height || canvas.height!),
      selectable: false,
      evented: false
    })
    
    canvas.setBackgroundImage(img, canvas.renderAll.bind(canvas))
    showProductAreas()
    canvas.renderAll()
  }, {
    crossOrigin: 'anonymous'
  })
}

const showProductAreas = () => {
  if (!canvas) return
  
  productAreas.value.forEach((area, index) => {
    const areaRect = new fabric.Rect({
      left: area.x,
      top: area.y,
      width: area.width,
      height: area.height,
      fill: 'rgba(0, 255, 0, 0.1)',
      stroke: 'rgba(0, 255, 0, 0.5)',
      strokeWidth: 2,
      strokeDashArray: [5, 5],
      selectable: false,
      evented: false,
      opacity: 0.7
    })
    
    const areaText = new fabric.Text(`Área ${index + 1}`, {
      left: area.x + area.width / 2,
      top: area.y + area.height / 2,
      fontSize: 16,
      fill: 'rgba(0, 150, 0, 0.8)',
      fontFamily: 'Inter',
      fontWeight: 'bold',
      textAlign: 'center',
      originX: 'center',
      originY: 'center',
      selectable: false,
      evented: false
    })
    
    canvas.add(areaRect)
    canvas.add(areaText)
  })
}

const addInitialTemplate = () => {
  if (!canvas) return
  loadSaoJoaoTemplate()
}

const zoomIn = () => {
  if (!canvas) return
  const newZoom = Math.min(zoom.value * 1.1, 3)
  zoom.value = newZoom
  canvas.setZoom(newZoom)
}

const zoomOut = () => {
  if (!canvas) return
  const newZoom = Math.max(zoom.value * 0.9, 0.1)
  zoom.value = newZoom
  canvas.setZoom(newZoom)
}

const undo = () => {
  console.log('Undo')
}

const redo = () => {
  console.log('Redo')
}

const updateSelectedProduct = () => {
  if (!selectedProduct.value || !canvas) return
  
  // Encontrar o produto no canvas e atualizar
  const productIndex = canvasProducts.value.findIndex(p => p.id === selectedProduct.value.id)
  if (productIndex !== -1) {
    canvasProducts.value[productIndex] = { ...selectedProduct.value }
    
    // Encontrar o grupo do produto no canvas e atualizar o preço
    const objects = canvas.getObjects()
    const productGroup = objects.find(obj => obj.productId === selectedProduct.value.id)
    
    if (productGroup && productGroup.type === 'group') {
      const groupObjects = productGroup.getObjects()
      
      // Encontrar e atualizar os textos de preço
      groupObjects.forEach((obj, index) => {
        if (obj.type === 'text') {
          // Atualizar valor principal (número antes da vírgula)
          if (index === 5) { // priceValue é index 5 agora
            obj.set('text', selectedProduct.value.price.split(',')[0] || '0')
          }
          // Atualizar centavos (número depois da vírgula)
          if (index === 6) { // centsValue é index 6 agora
            obj.set('text', ',' + (selectedProduct.value.price.split(',')[1] || '00'))
          }
        }
      })
      
      canvas.renderAll()
    }
  }
}

const addProductName = () => {
  if (!canvas) return
  
  const productName = new fabric.Text('Nome do Produto', {
    left: 100,
    top: 100,
    fontSize: 16,
    fill: '#1f2937',
    fontFamily: 'Inter',
    fontWeight: 'bold',
    selectable: true
  })
  
  canvas.add(productName)
  canvas.setActiveObject(productName)
  canvas.renderAll()
}

const addProductPrice = () => {
  if (!canvas) return
  
  const productPrice = new fabric.Text('R$ 0,00', {
    left: 100,
    top: 130,
    fontSize: 20,
    fill: '#dc2626',
    fontFamily: 'Inter',
    fontWeight: 'bold',
    selectable: true
  })
  
  canvas.add(productPrice)
  canvas.setActiveObject(productPrice)
  canvas.renderAll()
}
</script>

<style scoped>
/* Estilos para os sliders verdes */
.slider-green::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-green::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-green::-webkit-slider-track {
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(to right, #10b981 0%, #10b981 var(--value, 50%), #e5e7eb var(--value, 50%), #e5e7eb 100%);
}

.slider-green::-moz-range-track {
  height: 8px;
  border-radius: 4px;
  background: #e5e7eb;
}

.slider-green::-moz-range-progress {
  height: 8px;
  border-radius: 4px;
  background: #10b981;
}
</style>