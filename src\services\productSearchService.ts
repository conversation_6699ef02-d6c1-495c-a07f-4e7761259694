import { API_CONFIG, validateApiKeys } from '@/config/api'

export interface ProductSearchResult {
  id: string
  name: string
  category: string
  image: string
  processedImage?: string
  price: string
  removeBg: boolean
}

export class ProductSearchService {

  /**
   * Busca produtos reais usando Unsplash API
   */
  static async searchProducts(query: string): Promise<ProductSearchResult[]> {
    try {
      console.log(`Buscando produtos para: ${query}`)
      
      // Buscar imagens no Unsplash
      const unsplashUrl = `https://api.unsplash.com/search/photos?query=${encodeURIComponent(query + ' product')}&per_page=6&client_id=${API_CONFIG.UNSPLASH_ACCESS_KEY}`
      
      const response = await fetch(unsplashUrl)
      
      if (!response.ok) {
        throw new Error(`Erro na busca: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (!data.results || data.results.length === 0) {
        throw new Error('Nenhum produto encontrado')
      }

      // Converter resultados para formato do produto
      const products: ProductSearchResult[] = data.results.map((photo: any, index: number) => ({
        id: `search_${Date.now()}_${index}`,
        name: `${query} - ${photo.alt_description || 'Produto'}`,
        category: 'Encontrado Online',
        image: photo.urls.regular,
        price: this.generateRandomPrice(),
        removeBg: true
      }))

      console.log(`Encontrados ${products.length} produtos`)
      return products

    } catch (error) {
      console.error('Erro na busca de produtos:', error)
      throw new Error('Falha ao buscar produtos. Verifique sua conexão.')
    }
  }

  /**
   * Remove fundo da imagem usando Remove.bg API
   */
  static async removeBackground(imageUrl: string): Promise<string> {
    try {
      console.log('Removendo fundo da imagem...')
      
      const formData = new FormData()
      formData.append('image_url', imageUrl)
      formData.append('size', 'auto')

      const response = await fetch('https://api.remove.bg/v1.0/removebg', {
        method: 'POST',
        headers: {
          'X-Api-Key': API_CONFIG.REMOVEBG_API_KEY,
        },
        body: formData
      })

      if (!response.ok) {
        throw new Error(`Erro Remove.bg: ${response.status}`)
      }

      const blob = await response.blob()
      const processedImageUrl = URL.createObjectURL(blob)
      
      console.log('Fundo removido com sucesso')
      return processedImageUrl

    } catch (error) {
      console.error('Erro ao remover fundo:', error)
      // Retorna imagem original se falhar
      return imageUrl
    }
  }

  /**
   * Busca produtos e remove fundos automaticamente
   */
  static async searchProductsWithBackgroundRemoval(query: string): Promise<ProductSearchResult[]> {
    try {
      // Buscar produtos
      const products = await this.searchProducts(query)
      
      // Processar cada produto para remover fundo
      const processedProducts = await Promise.all(
        products.map(async (product) => {
          try {
            const processedImage = await this.removeBackground(product.image)
            return {
              ...product,
              processedImage
            }
          } catch (error) {
            console.warn(`Erro ao processar ${product.name}:`, error)
            return product
          }
        })
      )

      return processedProducts

    } catch (error) {
      console.error('Erro no processo completo:', error)
      throw error
    }
  }

  /**
   * Gera preço aleatório para produtos encontrados
   */
  private static generateRandomPrice(): string {
    const prices = [
      'R$ 5,99', 'R$ 8,50', 'R$ 12,90', 'R$ 15,75', 'R$ 18,99',
      'R$ 22,50', 'R$ 25,90', 'R$ 29,99', 'R$ 35,00', 'R$ 42,90'
    ]
    return prices[Math.floor(Math.random() * prices.length)]
  }

  /**
   * Valida se as APIs estão configuradas
   */
  static validateApiKeys(): { valid: boolean; missing: string[] } {
    return validateApiKeys()
  }
}
